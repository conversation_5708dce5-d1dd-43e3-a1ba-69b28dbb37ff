import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AutoActionHandler } from '../shared/auto-action.service';
import { War, WarStatus } from './entity/war.entity';
import { User } from '../user/entity/user.entity';
import { WarService } from './war.service';
import { ParticipateInWarDto } from './dto/participate-in-war.dto';

interface WarAutoSetting {
  userId: number;
  targetId: string; // War ID
}

@Injectable()
export class WarAutoHandler implements AutoActionHandler {
  private readonly logger = new Logger(WarAutoHandler.name);
  private autoSettings = new Map<string, WarAutoSetting>();

  constructor(
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @Inject(forwardRef(() => WarService))
    private warService: WarService,
  ) {}

  /**
   * Execute the auto war action
   * @param userId User ID
   * @param warId War ID
   * @param energyAmount Amount of energy to spend
   */
  async executeAction(
    userId: number,
    warId: string,
    energyAmount: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Executing auto war attack for user ${userId} in war ${warId} with ${energyAmount} energy`,
      );

      // Skip if no energy to spend
      if (energyAmount <= 0) {
        this.logger.log(`Skipping auto attack - no energy available`);
        return;
      }

      // Create participation DTO for the war service
      const participateDto: ParticipateInWarDto = {
        energyAmount: energyAmount,
        autoMode: false, // Don't trigger auto mode recursively
        autoEnergyPercentage: 100,
      };

      // Participate in the war using the existing war service logic
      await this.warService.participateInWar(userId, warId, participateDto);

      this.logger.log(
        `Auto war attack successful for user ${userId} in war ${warId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error executing auto war attack for user ${userId} in war ${warId}: ${error.message}`,
      );
      // Don't throw the error to prevent stopping the auto mode for temporary issues
      // unless it's a critical error that indicates the war is no longer valid
      if (error.message.includes('War not found') ||
          error.message.includes('War is already ended') ||
          error.message.includes('Can only participate in wars during the ground phase')) {
        throw error;
      }
    }
  }

  /**
   * Check if the war is still valid for auto attacks
   * @param userId User ID
   * @param warId War ID
   */
  async checkActionValidity(
    userId: number,
    warId: string,
  ): Promise<boolean> {
    try {
      // Check if war exists and is still active
      const war = await this.warRepository.findOne({
        where: { id: warId },
        relations: ['attackerRegion', 'defenderRegion'],
      });

      if (!war) {
        this.logger.log(`War ${warId} no longer exists`);
        return false;
      }

      // Check if war is still in active phase (not ended)
      if (war.status === WarStatus.ENDED) {
        this.logger.log(`War ${warId} has ended - stopping auto attack`);
        return false;
      }

      // Check if war is in an active phase (ground or revolution phase)
      if (war.status !== WarStatus.GROUND_PHASE && war.status !== WarStatus.REVOLUTION_PHASE) {
        this.logger.log(`War ${warId} is not in an active phase - current status: ${war.status}`);
        return false;
      }

      // Check if user exists and is premium
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['region'],
      });

      if (!user || !user.isPremium) {
        this.logger.log(
          `User ${userId} is no longer premium or does not exist`,
        );
        return false;
      }

      // Check if user is traveling (cannot participate while traveling)
      if (user.isTraveling) {
        this.logger.log(`User ${userId} is traveling - cannot participate in war`);
        return false;
      }

      // Check if user is still in a region that can participate in this war
      const isAttacker = user.region?.id === war.attackerRegion?.id;
      const isDefender = user.region?.id === war.defenderRegion?.id;

      if (!isAttacker && !isDefender) {
        this.logger.log(
          `User ${userId} is no longer in a region that can participate in war ${warId}`,
        );
        return false;
      }

      this.logger.log(
        `War ${warId} is valid for auto attack. Status: ${war.status}, User region: ${user.region?.id}, User premium: ${user.isPremium}`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Error checking validity for user ${userId} in war ${warId}: ${error.message}`,
      );
      // Return false for errors to stop auto mode if there are persistent issues
      return false;
    }
  }

  /**
   * Save auto war settings
   * @param userId User ID
   * @param warId War ID
   */
  async saveAutoSettings(
    userId: number,
    warId: string,
  ): Promise<void> {
    const key = `${userId}_${warId}`;
    this.autoSettings.set(key, { userId, targetId: warId });
    this.logger.log(
      `Saved auto war settings for user ${userId} in war ${warId}`,
    );
  }

  /**
   * Remove auto war settings
   * @param userId User ID
   * @param warId War ID
   */
  async removeAutoSettings(
    userId: number,
    warId: string,
  ): Promise<void> {
    const key = `${userId}_${warId}`;
    this.autoSettings.delete(key);
    this.logger.log(
      `Removed auto war settings for user ${userId} in war ${warId}`,
    );
  }

  /**
   * Get all active auto war settings
   * This method queries the database to find users with active war auto mode
   * This is critical for server restart scenarios
   */
  async getActiveAutoSettings(): Promise<{ userId: number; targetId: string }[]> {
    try {
      // Query users who have active war auto mode
      const usersWithAutoWar = await this.userRepository.find({
        where: {
          activeAutoMode: 'war' as any, // Cast to avoid enum issues
        },
        select: ['id', 'autoTargetId'],
      });

      const activeSettings = usersWithAutoWar
        .filter(user => user.autoTargetId) // Only include users with a target ID
        .map(user => ({
          userId: user.id,
          targetId: user.autoTargetId,
        }));

      this.logger.log(`Found ${activeSettings.length} users with active war auto mode in database`);

      // Also restore the in-memory settings for these users
      for (const setting of activeSettings) {
        const key = `${setting.userId}_${setting.targetId}`;
        this.autoSettings.set(key, setting);
      }

      return activeSettings;
    } catch (error) {
      this.logger.error(`Error getting active auto war settings: ${error.message}`);
      return [];
    }
  }
}
