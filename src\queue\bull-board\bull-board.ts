import { ExpressAdapter } from '@bull-board/express';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { Queue } from 'bullmq';

// Create the server adapter
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

// Create your BullMQ queue instance (match your queue config)
const autoActionQueue = new Queue('auto-actions', {
  connection: {
    host: process.env.REDIS_HOST || 'localhost',
    port: Number(process.env.REDIS_PORT) || 6379,
  },
});

// Create the bull board
createBullBoard({
  queues: [new BullMQAdapter(autoActionQueue)],
  serverAdapter,
});

// Export the router instead of a full Express app
export { serverAdapter };
