import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
} from 'typeorm';
import { Region } from '../../region/entity/region.entity';
import { User } from '../../user/entity/user.entity';
import { State } from '../../state/entity/state.entity';

export enum WarType {
  GROUND = 'ground',
  SEA = 'sea',
  AIR = 'air',
  REVOLUTION = 'revolution',
}

export enum WarTarget {
  CONQUEST = 'conquest',
  RESISTANCE = 'resistance',
  RESOURCES = 'resources',
  REVOLUTION = 'revolution',
}

export enum WarStatus {
  PENDING = 'pending',
  GROUND_PHASE = 'ground_phase',
  REVOLUTION_PHASE = 'revolution_phase',
  ENDED = 'ended',
}

@Entity()
export class War {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: WarType,
  })
  warType: WarType;

  @Column({
    type: 'enum',
    enum: WarStatus,
    default: WarStatus.PENDING,
  })
  status: WarStatus;

  @Column({
    type: 'enum',
    enum: WarTarget,
  })
  warTarget: WarTarget;

  @Column({ type: 'text' })
  declaration: string;

  @ManyToOne(() => State)
  attackerState: State;

  @ManyToOne(() => State)
  defenderState: State;

  @ManyToOne(() => Region)
  attackerRegion: Region;

  @ManyToOne(() => Region)
  defenderRegion: Region;

  @ManyToOne(() => Region)
  targetRegion: Region;

  @ManyToOne(() => User)
  declaredBy: User;

  @CreateDateColumn()
  declaredAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  endedAt: Date;

  @Column('float', { default: 0 })
  attackerGroundDamage: number;

  @Column('float', { default: 0 })
  defenderGroundDamage: number;

  @Column('float', { nullable: true })
  damageRequirement: number;

  @Column({ type: 'json', default: '[]' })
  battleEvents: {
    timestamp: Date;
    description: string;
    damage: number;
    side: 'attacker' | 'defender';
    userId: number;
  }[];

  @Column({ type: 'json', default: '{}' })
  participants: {
    attackers: {
      userId: number;
      username: string;
      damage: number;
      energySpent: number;
    }[];
    defenders: {
      userId: number;
      username: string;
      damage: number;
      energySpent: number;
    }[];
  };

  @Column('json', { nullable: true })
  resourcesTarget?: {
    type: string;
    amount: number;
  };

  @Column({ nullable: true })
  seaPhaseStartedAt: Date;

  @Column({ nullable: true })
  seaPhaseEndedAt: Date;

  @Column({ type: 'float', default: 0 })
  attackerSeaDamage: number;

  @Column({ type: 'float', default: 0 })
  defenderSeaDamage: number;

  @Column({ nullable: true })
  groundPhaseStartedAt: Date;
}
