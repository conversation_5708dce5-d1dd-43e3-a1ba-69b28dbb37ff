import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRevolutionPhase1748000000001 implements MigrationInterface {
  name = 'AddRevolutionPhase1748000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add revolution_phase to the war_status_enum
    await queryRunner.query(
      `ALTER TYPE "public"."war_status_enum" ADD VALUE 'revolution_phase'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type and updating all references
    // For now, we'll leave the enum value in place during rollback
    console.log('Warning: Cannot remove enum value revolution_phase from war_status_enum');
  }
}
