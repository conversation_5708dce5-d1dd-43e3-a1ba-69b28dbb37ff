import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  VirtualColumn,
} from 'typeorm';
import { Party } from '../../party/entity/party.entity';
import { User } from '../../user/entity/user.entity';
import { Factory } from '../../factory/entity/factory.entity';
import { State } from '../../state/entity/state.entity';
import { Expose } from 'class-transformer';

export enum RegionStatus {
  INDEPENDENT = 'independent',
  STATE_MEMBER = 'state_member',
  AUTONOMY = 'autonomy',
  // Later, you might add other categories.
}

@Entity()
export class Region {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  countryCode: string;

  // Political details
  @Column({ type: 'timestamp', nullable: true })
  parliamentCreatedAt: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  autonomyParliamentCreatedAt: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  lastRevolutionAt: Date | null;

  // War and damage info
  @Column('float', { default: 0 })
  initialAttackDamage: number; // e.g., 450000 * Military academy factor

  @Column('float', { default: 0 })
  initialDefendDamage: number; // e.g., 50000 * (building sum) + 450000 * Military academy factor

  // Environmental and economic details
  @Column('float', { default: 0 })
  pollution: number;

  @Column('float', { default: 0 })
  taxRate: number;

  @Column('float', { default: 0 })
  marketTaxes: number;

  // Factory output taxes per factory type, stored as a JSON object
  @Column({ type: 'json', nullable: true })
  factoryOutputTaxes: Record<string, number>;

  @Column({ default: false })
  seaAccess: boolean;

  // Resources: for each resource type, store the current and maximum levels
  @Column({ type: 'json', nullable: true })
  resources: {
    gold?: { current: number; max: number };
    oil?: { current: number; max: number };
    ore?: { current: number; max: number };
    uranium?: { current: number; max: number };
    diamonds?: { current: number; max: number };
  };

  // Regional indexes
  @Column('float', { default: 0 })
  healthIndex: number;

  @Column('float', { default: 0 })
  militaryIndex: number;

  @Column('float', { default: 0 })
  educationIndex: number;

  @Column('float', { default: 0 })
  developmentIndex: number;

  // Work permission flag: if true, players can work here without residency
  @Column({ default: false })
  residencyForWork: boolean;

  // Historical events
  @Column({ type: 'timestamp', nullable: true })
  lastRevolution: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  lastCoup: Date | null;

  @Column('float', { nullable: true })
  latitude: number;

  @Column('float', { nullable: true })
  longitude: number;

  // Borders with other regions (stored as a simple array of region IDs)
  @Column({ type: 'simple-array', nullable: true })
  bordersWith: string[];

  // Regional ranking (e.g., place in buildings rating)
  @Column({ default: 0 })
  topRating: number;

  // Regional status: independent or autonomy, etc.
  @Column({
    type: 'enum',
    enum: RegionStatus,
    default: RegionStatus.INDEPENDENT,
  })
  status: RegionStatus;

  @ManyToOne(() => State, (state) => state.regions, { nullable: true })
  state?: State | null;

  @OneToMany(() => Party, (party) => party.region)
  parties: Party[];

  @OneToMany(() => User, (user) => user.region)
  users: User[];

  @OneToMany(() => Factory, (factory) => factory.region)
  factories: Factory[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

@VirtualColumn({
  query: (alias) => `(SELECT COUNT(*) FROM "user" WHERE "user"."regionId" = ${alias}."id")`,
})
population: number;



@VirtualColumn({
  query: (alias) => `
    (SELECT STRING_AGG(r.name, ', ')
     FROM region r
     WHERE r."countryCode" = ANY(string_to_array(${alias}."bordersWith", ','))
    )
  `,
})
bordersWithNames: string[];


}
