import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Region } from '../../region/entity/region.entity';

@Entity('state')
export class State {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  flagUrl: string;

  @Column({ type: 'double precision', default: 0 })
  treasury: number;

  @Column('json', { default: {} })
  resourceReserves: {
    gold?: number;
    oil?: number;
    ore?: number;
    uranium?: number;
    diamonds?: number;
  };

  @Column('json', { default: [] })
  allies: string[];

  @Column('json', { default: [] })
  enemies: string[];

  @Column({ default: false })
  isActive: boolean;

  @Column({ default: false })
  hasOpenBorders: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, { nullable: true })
  leader: User;

  @OneToMany(() => Region, (region) => region.state, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  regions: Region[];
}
